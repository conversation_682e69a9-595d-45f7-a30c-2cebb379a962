
# URL-Shortener

A simple and fast URL shortener! This allows to convert long URLs into shorter, more manageable links, making them easier to share, tweet, or send via email.

### Installation
**1. Create a Folder where you want to save the project**

**2. Create a Virtual Environment and Activate**

Install Virtual Environment First
```
$  pip install virtualenv
```

Create Virtual Environment

For Windows
```
$  python -m venv venv
```
For Mac
```
$  python3 -m venv venv
```

Activate Virtual Environment

For Windows
```
$  source venv/scripts/activate
```

For Mac
```
$  source venv/bin/activate
```

**3. Clone this project**

```
$  git clone https://github.com/saminmahmud/URL-Shortener.git
```

Then, Enter the project
```
$  cd URL-Shortener
```

**4. Install Requirements from 'requirements.txt'**
```python
$  pip install -r requirements.txt
```

**5. Now Run Server**

Command for Windows:
```python
$ python manage.py runserver
```

Command for Linux or Mac:
```python
$ python3 manage.py runserver
```
